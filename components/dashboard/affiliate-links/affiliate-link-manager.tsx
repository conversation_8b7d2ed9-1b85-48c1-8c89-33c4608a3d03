"use client"
import { useState, useEffect } from "react"
import {
  ArrowDownIcon,
  ArrowUpIcon,
  BarChart3Icon,
  CheckIcon,
  ChevronDownIcon,
  CopyIcon,
  DownloadIcon,
  FilterIcon,
  MoreHorizontalIcon,
  PauseIcon,
  PlayIcon,
  PlusIcon,
  QrCodeIcon,
  SearchIcon,
  TrashIcon,
} from "lucide-react"
import { toast } from "sonner"

import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { QRCodeModal } from "./qr-code-modal"
import { CreateLinkWizard } from "./create-link-wizard"
import { LinkCard } from "./components/LinkCard"
import { StatusBadge } from "./components/StatusBadge"
import { ChangeIndicator } from "./components/ChangeIndicator"
import { LinkPerformanceChart } from "./components/LinkPerformanceChart"

// Types
interface AffiliateLink {
  id: string
  campaign: string
  campaignLogo: string
  link: string
  clicks: number
  clicksChange: number
  earnings: number
  earningsChange: number
  status: "active" | "paused" | "completed"
  performance: { date: string; clicks: number }[]
}

// Sample data
const sampleAffiliateLinks: AffiliateLink[] = [
  {
    id: "1",
    campaign: "Summer Fashion Collection",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/summer-fashion",
    clicks: 1245,
    clicksChange: 12,
    earnings: 623.45,
    earningsChange: 8,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 100) + 20,
    })),
  },
  {
    id: "2",
    campaign: "Tech Gadgets Promo",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/tech-gadgets",
    clicks: 876,
    clicksChange: -5,
    earnings: 438.2,
    earningsChange: -3,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 80) + 10,
    })),
  },
  {
    id: "3",
    campaign: "Home Decor Sale",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/home-decor",
    clicks: 532,
    clicksChange: 3,
    earnings: 266.0,
    earningsChange: 5,
    status: "paused",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 60) + 5,
    })),
  },
  {
    id: "4",
    campaign: "Fitness Equipment",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/fitness",
    clicks: 1089,
    clicksChange: 15,
    earnings: 544.5,
    earningsChange: 18,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 90) + 30,
    })),
  },
  {
    id: "5",
    campaign: "Beauty Products Bundle",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/beauty",
    clicks: 765,
    clicksChange: 7,
    earnings: 382.5,
    earningsChange: 9,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 70) + 15,
    })),
  },
  {
    id: "6",
    campaign: "Travel Package Deals",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/travel",
    clicks: 421,
    clicksChange: -8,
    earnings: 210.5,
    earningsChange: -10,
    status: "completed",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 50) + 5,
    })),
  },
  {
    id: "7",
    campaign: "Online Course Promotion",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/courses",
    clicks: 932,
    clicksChange: 20,
    earnings: 466.0,
    earningsChange: 22,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 85) + 25,
    })),
  },
  {
    id: "8",
    campaign: "Food Delivery Service",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/food-delivery",
    clicks: 654,
    clicksChange: 5,
    earnings: 327.0,
    earningsChange: 4,
    status: "paused",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 65) + 10,
    })),
  },
]

export function AffiliateLinkManager() {
  const isMobile = useIsMobile()
  const [links, setLinks] = useState<AffiliateLink[]>(sampleAffiliateLinks)
  const [filteredLinks, setFilteredLinks] = useState<AffiliateLink[]>(links)
  const [selectedLinks, setSelectedLinks] = useState<string[]>([])
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [qrModalOpen, setQrModalOpen] = useState<boolean>(false)
  const [createModalOpen, setCreateModalOpen] = useState<boolean>(false)
  const [selectedLink, setSelectedLink] = useState<AffiliateLink | null>(null)

  // Apply filters when links, statusFilter, or searchQuery changes
  useEffect(() => {
    let filtered = [...links]

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((link) => link.status === statusFilter)
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (link) => link.campaign.toLowerCase().includes(query) || link.link.toLowerCase().includes(query),
      )
    }

    setFilteredLinks(filtered)
  }, [links, statusFilter, searchQuery])

  // Handle select all checkbox
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedLinks(filteredLinks.map((link) => link.id))
    } else {
      setSelectedLinks([])
    }
  }

  // Handle individual checkbox
  const handleSelectLink = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedLinks((prev) => [...prev, id])
    } else {
      setSelectedLinks((prev) => prev.filter((linkId) => linkId !== id))
    }
  }

  // Handle copy link
  const handleCopyLink = (link: string) => {
    navigator.clipboard.writeText(link)
    toast.success("Link copied to clipboard")
  }

  // Handle QR code generation
  const handleGenerateQR = (link: AffiliateLink) => {
    setSelectedLink(link)
    setQrModalOpen(true)
  }

  // Handle view analytics
  const handleViewAnalytics = (linkId: string) => {
    window.location.href = `/dashboard/affiliate-links/${linkId}/analytics`
  }

  // Handle status change
  const handleStatusChange = (id: string, status: "active" | "paused" | "completed") => {
    setLinks((prev) => prev.map((link) => (link.id === id ? { ...link, status } : link)))
    toast.success(`Link status changed to ${status}`)
  }

  // Handle bulk status change
  const handleBulkStatusChange = (status: "active" | "paused" | "completed") => {
    setLinks((prev) => prev.map((link) => (selectedLinks.includes(link.id) ? { ...link, status } : link)))
    toast.success(`${selectedLinks.length} links updated to ${status}`)
    setSelectedLinks([])
  }

  // Handle bulk export
  const handleBulkExport = () => {
    const selectedData = links.filter((link) => selectedLinks.includes(link.id))
    const jsonData = JSON.stringify(selectedData, null, 2)
    const blob = new Blob([jsonData], { type: "application/json" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = "affiliate-links-export.json"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success(`${selectedLinks.length} links exported`)
  }

  // Handle link creation
  const handleLinkCreated = (newLink: AffiliateLink) => {
    setLinks((prev) => [newLink, ...prev])
    toast.success("New affiliate link created successfully")
  }

  // Render mobile card view
  const renderMobileView = () => (
    <div className="grid gap-4">
      {filteredLinks.map((link) => (
        <LinkCard
          key={link.id}
          link={link}
          isSelected={selectedLinks.includes(link.id)}
          onSelect={handleSelectLink}
          onCopyLink={handleCopyLink}
          onGenerateQR={handleGenerateQR}
          onViewAnalytics={handleViewAnalytics}
          onStatusChange={handleStatusChange}
        />
      ))}
    </div>
  )

  // Render desktop table view
  const renderDesktopView = () => (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40px]">
              <Checkbox
                checked={filteredLinks.length > 0 && selectedLinks.length === filteredLinks.length}
                onCheckedChange={handleSelectAll}
                aria-label="Select all"
              />
            </TableHead>
            <TableHead>Campaign</TableHead>
            <TableHead>Link</TableHead>
            <TableHead>Clicks</TableHead>
            <TableHead>Earnings</TableHead>
            <TableHead>Performance</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredLinks.map((link) => (
            <TableRow key={link.id}>
              <TableCell>
                <Checkbox
                  checked={selectedLinks.includes(link.id)}
                  onCheckedChange={(checked) => handleSelectLink(link.id, !!checked)}
                  aria-label={`Select ${link.campaign}`}
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <img
                    src={link.campaignLogo || "/placeholder.svg"}
                    alt={link.campaign}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                  <span className="font-medium">{link.campaign}</span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <span className="max-w-[200px] truncate">{link.link}</span>
                  <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => handleCopyLink(link.link)}>
                    <CopyIcon className="h-3 w-3" />
                    <span className="sr-only">Copy link</span>
                  </Button>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <span className="font-medium">{link.clicks.toLocaleString()}</span>
                  <ChangeIndicator value={link.clicksChange} />
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <span className="font-medium">${link.earnings.toFixed(2)}</span>
                  <ChangeIndicator value={link.earningsChange} />
                </div>
              </TableCell>
              <TableCell>
                <div className="w-[100px]">
                  <LinkPerformanceChart
                    data={link.performance}
                    linkId={link.id}
                    dataKey="clicks"
                    height={40}
                  />
                </div>
              </TableCell>
              <TableCell>
                <StatusBadge status={link.status} />
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-2">
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleViewAnalytics(link.id)}>
                    <BarChart3Icon className="h-4 w-4" />
                    <span className="sr-only">View Analytics</span>
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleGenerateQR(link)}>
                    <QrCodeIcon className="h-4 w-4" />
                    <span className="sr-only">Generate QR Code</span>
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontalIcon className="h-4 w-4" />
                        <span className="sr-only">Actions</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleViewAnalytics(link.id)}>
                        <BarChart3Icon className="mr-2 h-4 w-4" />
                        View Analytics
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {link.status !== "active" && (
                        <DropdownMenuItem onClick={() => handleStatusChange(link.id, "active")}>
                          <PlayIcon className="mr-2 h-4 w-4" />
                          Activate
                        </DropdownMenuItem>
                      )}
                      {link.status !== "paused" && (
                        <DropdownMenuItem onClick={() => handleStatusChange(link.id, "paused")}>
                          <PauseIcon className="mr-2 h-4 w-4" />
                          Pause
                        </DropdownMenuItem>
                      )}
                      {link.status !== "completed" && (
                        <DropdownMenuItem onClick={() => handleStatusChange(link.id, "completed")}>
                          <CheckIcon className="mr-2 h-4 w-4" />
                          Mark as Completed
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleCopyLink(link.link)}>
                        <CopyIcon className="mr-2 h-4 w-4" />
                        Copy Link
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleGenerateQR(link)}>
                        <QrCodeIcon className="mr-2 h-4 w-4" />
                        Generate QR Code
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )

  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Button onClick={() => setCreateModalOpen(true)}>
            <PlusIcon className="mr-1 h-4 w-4" />
            Create Link
          </Button>
          <div className="relative w-full sm:w-72">
            <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search campaigns or links..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="h-9 w-[180px] sm:w-[130px]">
              <div className="flex items-center gap-2">
                <FilterIcon className="h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Status</SelectLabel>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          {selectedLinks.length > 0 && (
            <>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9">
                    Bulk Actions
                    <ChevronDownIcon className="ml-1 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleBulkStatusChange("active")}>
                    <PlayIcon className="mr-2 h-4 w-4" />
                    Activate Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkStatusChange("paused")}>
                    <PauseIcon className="mr-2 h-4 w-4" />
                    Pause Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkStatusChange("completed")}>
                    <CheckIcon className="mr-2 h-4 w-4" />
                    Mark Selected as Completed
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleBulkExport}>
                    <DownloadIcon className="mr-2 h-4 w-4" />
                    Export Selected
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-destructive">
                    <TrashIcon className="mr-2 h-4 w-4" />
                    Delete Selected
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button variant="outline" size="sm" className="h-9" onClick={handleBulkExport}>
                <DownloadIcon className="mr-1 h-4 w-4" />
                Export ({selectedLinks.length})
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Selected count */}
      {selectedLinks.length > 0 && (
        <div className="text-sm text-muted-foreground">
          {selectedLinks.length} of {filteredLinks.length} links selected
        </div>
      )}

      {/* Links table/cards */}
      {filteredLinks.length === 0 ? (
        <div className="flex h-[300px] flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
          <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
            <h3 className="mt-4 text-lg font-semibold">No affiliate links found</h3>
            <p className="mb-4 mt-2 text-sm text-muted-foreground">
              {searchQuery || statusFilter !== "all"
                ? "Try adjusting your search or filter to find what you're looking for."
                : "You don't have any affiliate links yet. Create your first campaign to get started."}
            </p>
            <Button onClick={() => setCreateModalOpen(true)}>Create Affiliate Link</Button>
          </div>
        </div>
      ) : isMobile ? (
        renderMobileView()
      ) : (
        renderDesktopView()
      )}

      {/* QR Code Modal */}
      <QRCodeModal
        open={qrModalOpen}
        onOpenChange={setQrModalOpen}
        link={selectedLink?.link || ""}
        campaign={selectedLink?.campaign || ""}
      />

      {/* Create Link Wizard */}
      <CreateLinkWizard open={createModalOpen} onOpenChange={setCreateModalOpen} onLinkCreated={handleLinkCreated} />
    </div>
  )
}


