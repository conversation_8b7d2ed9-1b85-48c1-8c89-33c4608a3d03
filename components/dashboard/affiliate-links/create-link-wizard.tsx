"use client"
import { useState, useEffect, useCallback } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { ArrowLeft, ArrowRight, Check, Copy, ExternalLink, Globe, Loader2, Plus, Save, Sparkles } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Sample campaign data
const sampleCampaigns = [
  {
    id: "1",
    name: "Summer Fashion Collection",
    logo: "/placeholder.svg?height=40&width=40",
    baseUrl: "https://example.com/summer-fashion",
    commission: "5% per sale",
    status: "active",
  },
  {
    id: "2",
    name: "Tech Gadgets Promo",
    logo: "/placeholder.svg?height=40&width=40",
    baseUrl: "https://example.com/tech-gadgets",
    commission: "$10 per lead",
    status: "active",
  },
  {
    id: "3",
    name: "Home Decor Sale",
    logo: "/placeholder.svg?height=40&width=40",
    baseUrl: "https://example.com/home-decor",
    commission: "8% per sale",
    status: "active",
  },
  {
    id: "4",
    name: "Fitness Equipment",
    logo: "/placeholder.svg?height=40&width=40",
    baseUrl: "https://example.com/fitness",
    commission: "12% per sale",
    status: "active",
  },
]

// Form schema
const linkFormSchema = z.object({
  campaignId: z.string().min(1, "Please select a campaign"),
  destinationUrl: z.string().url("Please enter a valid URL"),
  customPath: z.string().optional(),
  linkName: z.string().min(3, "Link name must be at least 3 characters"),
  utmSource: z.string().optional(),
  utmMedium: z.string().optional(),
  utmCampaign: z.string().optional(),
  utmTerm: z.string().optional(),
  utmContent: z.string().optional(),
  addUniqueId: z.boolean().default(true),
  notes: z.string().optional(),
})

type LinkFormValues = z.infer<typeof linkFormSchema>

// Default form values
const defaultValues: Partial<LinkFormValues> = {
  campaignId: "",
  destinationUrl: "",
  customPath: "",
  linkName: "",
  utmSource: "affiliate",
  utmMedium: "referral",
  utmCampaign: "",
  utmTerm: "",
  utmContent: "",
  addUniqueId: true,
  notes: "",
}

// Wizard steps
const steps = [
  { id: "campaign", title: "Select Campaign" },
  { id: "destination", title: "Destination URL" },
  { id: "tracking", title: "Tracking Parameters" },
  { id: "customize", title: "Customize Link" },
  { id: "review", title: "Review & Create" },
]

interface CreateLinkWizardProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLinkCreated?: (link: any) => void
}

export function CreateLinkWizard({ open, onOpenChange, onLinkCreated }: CreateLinkWizardProps) {
  const [step, setStep] = useState(0)
  const [selectedCampaign, setSelectedCampaign] = useState<any>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [previewLink, setPreviewLink] = useState("")
  const [linkCreated, setLinkCreated] = useState(false)
  // Generate a stable reference for the unique ID
  const [uniqueId] = useState(() => Math.random().toString(36).substring(2, 8))

  // Initialize form
  const form = useForm<LinkFormValues>({
    resolver: zodResolver(linkFormSchema),
    defaultValues,
    mode: "onChange",
  })

  // Watch form values for preview link generation
  const watchedValues = form.watch()

  // Update preview link when form values change - memoized to prevent infinite loops
  const generatePreviewLink = useCallback(() => {
    if (!selectedCampaign) return ""

    let baseUrl = watchedValues.destinationUrl || selectedCampaign.baseUrl

    // Add UTM parameters if they exist
    const utmParams = []
    if (watchedValues.utmSource) utmParams.push(`utm_source=${encodeURIComponent(watchedValues.utmSource)}`)
    if (watchedValues.utmMedium) utmParams.push(`utm_medium=${encodeURIComponent(watchedValues.utmMedium)}`)
    if (watchedValues.utmCampaign) utmParams.push(`utm_campaign=${encodeURIComponent(watchedValues.utmCampaign)}`)
    if (watchedValues.utmTerm) utmParams.push(`utm_term=${encodeURIComponent(watchedValues.utmTerm)}`)
    if (watchedValues.utmContent) utmParams.push(`utm_content=${encodeURIComponent(watchedValues.utmContent)}`)

    // Add unique ID if selected - using the stable uniqueId
    if (watchedValues.addUniqueId) {
      utmParams.push(`ref=aff_${uniqueId}`)
    }

    // Add custom path if provided
    if (watchedValues.customPath) {
      // Remove trailing slash from base URL if it exists
      baseUrl = baseUrl.replace(/\/$/, "")
      // Add slash to custom path if it doesn't exist
      const customPath = watchedValues.customPath.startsWith("/")
        ? watchedValues.customPath
        : `/${watchedValues.customPath}`
      baseUrl = `${baseUrl}${customPath}`
    }

    // Combine URL and parameters
    if (utmParams.length > 0) {
      baseUrl += (baseUrl.includes("?") ? "&" : "?") + utmParams.join("&")
    }

    return baseUrl
  }, [watchedValues, selectedCampaign, uniqueId])

  // Update preview link when form values change
  useEffect(() => {
    if (selectedCampaign) {
      setPreviewLink(generatePreviewLink())
    }
  }, [generatePreviewLink, selectedCampaign])

  // Handle campaign selection
  const handleCampaignSelect = (campaign: any) => {
    setSelectedCampaign(campaign)
    form.setValue("campaignId", campaign.id)
    form.setValue("destinationUrl", campaign.baseUrl || "")
    form.setValue("utmCampaign", campaign.name ? campaign.name.toLowerCase().replace(/\s+/g, "-") : "")
  }

  // Handle form submission
  const onSubmit = async (data: LinkFormValues) => {
    setIsCreating(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      const newLink = {
        id: Math.random().toString(36).substring(2, 9),
        campaign: sampleCampaigns.find((c) => c.id === data.campaignId)?.name || "",
        campaignLogo: sampleCampaigns.find((c) => c.id === data.campaignId)?.logo || "",
        link: previewLink,
        clicks: 0,
        clicksChange: 0,
        earnings: 0,
        earningsChange: 0,
        status: "active",
        performance: Array.from({ length: 30 }, (_, i) => ({
          date: `2023-06-${i + 1}`,
          clicks: 0,
        })),
        createdAt: new Date().toISOString(),
        name: data.linkName,
        notes: data.notes,
      }

      // Call onLinkCreated callback if provided
      if (onLinkCreated) {
        onLinkCreated(newLink)
      }

      setLinkCreated(true)
      toast.success("Affiliate link created successfully")
    } catch (error) {
      toast.error("Failed to create affiliate link")
    } finally {
      setIsCreating(false)
    }
  }

  // Handle next step
  const handleNext = async () => {
    // Validate current step fields
    let fieldsToValidate: (keyof LinkFormValues)[] = []

    switch (step) {
      case 0: // Campaign
        fieldsToValidate = ["campaignId"]
        break
      case 1: // Destination
        fieldsToValidate = ["destinationUrl"]
        break
      case 2: // Tracking
        // No required fields in this step
        break
      case 3: // Customize
        fieldsToValidate = ["linkName"]
        break
      default:
        break
    }

    // Validate fields for current step
    const isValid = await form.trigger(fieldsToValidate)

    if (isValid) {
      // If on last step, submit form
      if (step === steps.length - 1) {
        form.handleSubmit(onSubmit)()
      } else {
        // Move to next step
        setStep((prev) => Math.min(prev + 1, steps.length - 1))
      }
    }
  }

  // Handle previous step
  const handlePrevious = () => {
    setStep((prev) => Math.max(prev - 1, 0))
  }

  // Handle dialog close
  const handleDialogClose = (open: boolean) => {
    if (!open) {
      // Reset form and state if dialog is closed
      if (!linkCreated) {
        form.reset(defaultValues)
        setStep(0)
        setSelectedCampaign(null)
        setPreviewLink("")
      }
      setLinkCreated(false)
    }
    onOpenChange(open)
  }

  // Handle create another link
  const handleCreateAnother = () => {
    form.reset(defaultValues)
    setStep(0)
    setSelectedCampaign(null)
    setPreviewLink("")
    setLinkCreated(false)
  }

  // Copy link to clipboard
  const copyLinkToClipboard = () => {
    navigator.clipboard.writeText(previewLink)
    toast.success("Link copied to clipboard")
  }

  // Generate random values for UTM parameters
  const generateRandomUtmParams = () => {
    const randomId = Math.random().toString(36).substring(2, 8)
    form.setValue("utmContent", `content_${randomId}`)
    form.setValue("utmTerm", `term_${randomId}`)
    toast.success("Random parameters generated")
  }

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[700px]">
        {!linkCreated ? (
          <>
            <DialogHeader>
              <DialogTitle>Create Affiliate Link</DialogTitle>
              <DialogDescription>
                Generate a new affiliate link in just a few steps. Track performance and earn commissions.
              </DialogDescription>
            </DialogHeader>

            {/* Progress steps */}
            <div className="relative mb-4 mt-2">
              <div className="absolute left-0 top-[15px] h-[2px] w-full bg-muted" />
              <div className="relative z-10 flex justify-between">
                {steps.map((s, i) => (
                  <div key={s.id} className="flex flex-col items-center">
                    <div
                      className={cn(
                        "flex h-8 w-8 items-center justify-center rounded-full border-2 border-muted bg-background text-sm font-semibold",
                        step > i && "border-primary bg-primary text-primary-foreground",
                        step === i && "border-primary text-primary",
                      )}
                    >
                      {step > i ? <Check className="h-4 w-4" /> : i + 1}
                    </div>
                    <span
                      className={cn(
                        "mt-1 text-xs font-medium text-muted-foreground",
                        step === i && "text-primary",
                        step > i && "text-primary",
                      )}
                    >
                      {s.title}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Step 1: Select Campaign */}
                {step === 0 && (
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="campaignId"
                      render={({ field }) => (
                        <FormItem className="hidden">
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <div className="grid gap-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium">Select a Campaign</h3>
                        <Button variant="outline" size="sm">
                          <Plus className="mr-1 h-3 w-3" />
                          New Campaign
                        </Button>
                      </div>

                      <div className="grid gap-3">
                        {sampleCampaigns.map((campaign) => (
                          <div
                            key={campaign.id}
                            className={cn(
                              "flex cursor-pointer items-center gap-3 rounded-lg border p-3 transition-colors hover:bg-accent",
                              selectedCampaign?.id === campaign.id && "border-primary bg-primary/5",
                            )}
                            onClick={() => handleCampaignSelect(campaign)}
                          >
                            <div className="flex h-10 w-10 shrink-0 items-center justify-center overflow-hidden rounded-full bg-muted">
                              <img
                                src={campaign.logo || "/placeholder.svg"}
                                alt={campaign.name}
                                className="h-full w-full object-cover"
                              />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">{campaign.name}</h4>
                                <Badge variant="outline" className="capitalize">
                                  {campaign.status}
                                </Badge>
                              </div>
                              <div className="mt-1 flex items-center gap-3 text-sm text-muted-foreground">
                                <span className="flex items-center gap-1">
                                  <Globe className="h-3 w-3" />
                                  {campaign.baseUrl}
                                </span>
                                <Separator orientation="vertical" className="h-3" />
                                <span>{campaign.commission}</span>
                              </div>
                            </div>
                            <RadioGroup value={selectedCampaign?.id === campaign.id ? "selected" : ""}>
                              <RadioGroupItem value="selected" id={`campaign-${campaign.id}`} />
                            </RadioGroup>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2: Destination URL */}
                {step === 1 && (
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Destination URL</h3>
                      <p className="text-sm text-muted-foreground">
                        Set the destination URL for your affiliate link. This is where users will be directed.
                      </p>
                    </div>

                    <div className="rounded-md border bg-muted/50 p-3">
                      <div className="flex items-center gap-2">
                        <img
                          src={selectedCampaign?.logo || "/placeholder.svg"}
                          alt={selectedCampaign?.name}
                          className="h-8 w-8 rounded-full object-cover"
                        />
                        <div>
                          <h4 className="font-medium">{selectedCampaign?.name}</h4>
                          <p className="text-xs text-muted-foreground">{selectedCampaign?.commission}</p>
                        </div>
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="destinationUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Destination URL</FormLabel>
                          <FormControl>
                            <div className="flex items-center gap-2">
                              <Input {...field} placeholder="https://example.com/product-page" />
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="icon"
                                      onClick={() => window.open(field.value, "_blank")}
                                      disabled={!field.value}
                                    >
                                      <ExternalLink className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>Test this URL</TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </FormControl>
                          <FormDescription>Enter the specific landing page URL you want to promote.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="customPath"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Path (Optional)</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <span className="rounded-l-md border border-r-0 bg-muted px-3 py-2 text-sm text-muted-foreground">
                                {watchedValues.destinationUrl || selectedCampaign?.baseUrl || "https://example.com"}/
                              </span>
                              <Input {...field} className="rounded-l-none" placeholder="special-offer" />
                            </div>
                          </FormControl>
                          <FormDescription>
                            Add a custom path to make your link more descriptive or for A/B testing.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                {/* Step 3: Tracking Parameters */}
                {step === 2 && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium">Tracking Parameters</h3>
                        <p className="text-sm text-muted-foreground">
                          Add UTM parameters to track the performance of your affiliate link.
                        </p>
                      </div>
                      <Button type="button" variant="outline" size="sm" onClick={generateRandomUtmParams}>
                        <Sparkles className="mr-1 h-3 w-3" />
                        Generate Random
                      </Button>
                    </div>

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="utmSource"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>UTM Source</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="affiliate" />
                            </FormControl>
                            <FormDescription>Identifies which site sent the traffic (e.g., affiliate)</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="utmMedium"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>UTM Medium</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="referral" />
                            </FormControl>
                            <FormDescription>Identifies the marketing medium (e.g., referral, social)</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="utmCampaign"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>UTM Campaign</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="summer-promo" />
                            </FormControl>
                            <FormDescription>Identifies a specific product promotion or campaign</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="utmTerm"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>UTM Term (Optional)</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="running-shoes" />
                            </FormControl>
                            <FormDescription>Identifies search terms or specific content</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="utmContent"
                        render={({ field }) => (
                          <FormItem className="sm:col-span-2">
                            <FormLabel>UTM Content (Optional)</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="summer-banner-1" />
                            </FormControl>
                            <FormDescription>
                              Identifies what specifically was clicked (e.g., banner-1, text-link)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="addUniqueId"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Add Unique Identifier</FormLabel>
                            <FormDescription>
                              Automatically add a unique reference ID to each link for better tracking
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                {/* Step 4: Customize Link */}
                {step === 3 && (
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Customize Link</h3>
                      <p className="text-sm text-muted-foreground">
                        Add additional information to help you organize and manage your affiliate links.
                      </p>
                    </div>

                    <FormField
                      control={form.control}
                      name="linkName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Link Name</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Summer Promotion - Instagram" />
                          </FormControl>
                          <FormDescription>
                            A descriptive name to help you identify this link in your dashboard
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder="Add any notes about this link, such as where you plan to use it"
                              className="min-h-[100px]"
                            />
                          </FormControl>
                          <FormDescription>Private notes for your reference only</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="rounded-lg border p-4">
                      <h4 className="mb-2 font-medium">Link Preview</h4>
                      <div className="break-all rounded bg-muted p-2 text-sm font-mono">
                        {previewLink || "https://example.com/your-affiliate-link"}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 5: Review & Create */}
                {step === 4 && (
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Review & Create</h3>
                      <p className="text-sm text-muted-foreground">
                        Review your affiliate link details before creating it.
                      </p>
                    </div>

                    <div className="rounded-lg border">
                      <div className="border-b p-4">
                        <div className="flex items-center gap-3">
                          <img
                            src={selectedCampaign?.logo || "/placeholder.svg"}
                            alt={selectedCampaign?.name}
                            className="h-10 w-10 rounded-full object-cover"
                          />
                          <div>
                            <h4 className="font-medium">{selectedCampaign?.name}</h4>
                            <p className="text-sm text-muted-foreground">{selectedCampaign?.commission}</p>
                          </div>
                        </div>
                      </div>

                      <div className="grid gap-0.5 p-4 text-sm">
                        <div className="grid grid-cols-3 gap-2 py-1">
                          <div className="font-medium text-muted-foreground">Link Name</div>
                          <div className="col-span-2">{watchedValues.linkName}</div>
                        </div>
                        <div className="grid grid-cols-3 gap-2 py-1">
                          <div className="font-medium text-muted-foreground">Destination URL</div>
                          <div className="col-span-2 break-all">{watchedValues.destinationUrl}</div>
                        </div>
                        {watchedValues.customPath && (
                          <div className="grid grid-cols-3 gap-2 py-1">
                            <div className="font-medium text-muted-foreground">Custom Path</div>
                            <div className="col-span-2">{watchedValues.customPath}</div>
                          </div>
                        )}
                        <div className="grid grid-cols-3 gap-2 py-1">
                          <div className="font-medium text-muted-foreground">UTM Parameters</div>
                          <div className="col-span-2">
                            <div className="flex flex-wrap gap-1">
                              {watchedValues.utmSource && (
                                <Badge variant="outline">source: {watchedValues.utmSource}</Badge>
                              )}
                              {watchedValues.utmMedium && (
                                <Badge variant="outline">medium: {watchedValues.utmMedium}</Badge>
                              )}
                              {watchedValues.utmCampaign && (
                                <Badge variant="outline">campaign: {watchedValues.utmCampaign}</Badge>
                              )}
                              {watchedValues.utmTerm && <Badge variant="outline">term: {watchedValues.utmTerm}</Badge>}
                              {watchedValues.utmContent && (
                                <Badge variant="outline">content: {watchedValues.utmContent}</Badge>
                              )}
                              {!watchedValues.utmSource &&
                                !watchedValues.utmMedium &&
                                !watchedValues.utmCampaign &&
                                !watchedValues.utmTerm &&
                                !watchedValues.utmContent && (
                                  <span className="text-muted-foreground">None specified</span>
                                )}
                            </div>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-2 py-1">
                          <div className="font-medium text-muted-foreground">Unique Identifier</div>
                          <div className="col-span-2">{watchedValues.addUniqueId ? "Yes" : "No"}</div>
                        </div>
                        {watchedValues.notes && (
                          <div className="grid grid-cols-3 gap-2 py-1">
                            <div className="font-medium text-muted-foreground">Notes</div>
                            <div className="col-span-2">{watchedValues.notes}</div>
                          </div>
                        )}
                      </div>

                      <div className="border-t p-4">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">Final Affiliate Link</h4>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-8 gap-1"
                              onClick={copyLinkToClipboard}
                            >
                              <Copy className="h-3 w-3" />
                              Copy
                            </Button>
                          </div>
                          <div className="break-all rounded bg-muted p-2 text-sm font-mono">{previewLink}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </Form>

            <DialogFooter className="flex items-center justify-between">
              <Button type="button" variant="outline" onClick={handlePrevious} disabled={step === 0}>
                <ArrowLeft className="mr-1 h-4 w-4" />
                Back
              </Button>
              <div className="flex items-center gap-2">
                {step === steps.length - 1 ? (
                  <Button type="button" onClick={handleNext} disabled={isCreating}>
                    {isCreating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="mr-1 h-4 w-4" />
                        Create Link
                      </>
                    )}
                  </Button>
                ) : (
                  <Button type="button" onClick={handleNext}>
                    Next
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Button>
                )}
              </div>
            </DialogFooter>
          </>
        ) : (
          // Success state
          <div className="flex flex-col items-center justify-center py-6">
            <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400">
              <Check className="h-6 w-6" />
            </div>
            <h2 className="mb-1 text-xl font-semibold">Affiliate Link Created!</h2>
            <p className="mb-6 text-center text-muted-foreground">
              Your new affiliate link has been created successfully and is ready to use.
            </p>

            <div className="mb-6 w-full space-y-2 rounded-lg border p-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Your Affiliate Link</h4>
                <Button type="button" variant="ghost" size="sm" className="h-8 gap-1" onClick={copyLinkToClipboard}>
                  <Copy className="h-3 w-3" />
                  Copy
                </Button>
              </div>
              <div className="break-all rounded bg-muted p-2 text-sm font-mono">{previewLink}</div>
            </div>

            <div className="flex w-full flex-col gap-2 sm:flex-row">
              <Button variant="outline" className="w-full" onClick={handleCreateAnother}>
                <Plus className="mr-1 h-4 w-4" />
                Create Another Link
              </Button>
              <Button className="w-full" onClick={() => handleDialogClose(false)}>
                <Check className="mr-1 h-4 w-4" />
                Done
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
